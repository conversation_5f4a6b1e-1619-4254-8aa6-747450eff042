// installer.cpp : 定义应用程序的入口点。
//

#include "resource.h"
#include "framework.h"
#include "installer.h"
#include <wininet.h>
#include <shlobj.h>
#include <shlwapi.h>
#include <shellapi.h>
#include <iostream>
#include <string>
#include <fstream>
#include <sstream>
#include <vector>
#include <filesystem>
#include <regex>
#include <commctrl.h>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "comctl32.lib")

// 确保IDC_OPEN_BUTTON被定义
#ifndef IDC_OPEN_BUTTON
#define IDC_OPEN_BUTTON 1006
#endif

#ifndef IDC_PROGRESS
#define IDC_PROGRESS 1001
#endif

#ifndef IDC_STATUS_TEXT
#define IDC_STATUS_TEXT 1002
#endif

#ifndef IDC_START_BUTTON
#define IDC_START_BUTTON 1004
#endif

#ifndef IDC_DETAIL_TEXT
#define IDC_DETAIL_TEXT 1005
#endif

#ifndef IDD_INSTALLER_DIALOG
#define IDD_INSTALLER_DIALOG 102
#endif

#define MAX_LOADSTRING 100
#define BUFFER_SIZE 4096

// 全局变量:
HINSTANCE hInst;                                // 当前实例
WCHAR szTitle[MAX_LOADSTRING];                  // 标题栏文本
WCHAR szWindowClass[MAX_LOADSTRING];            // 主窗口类名
HWND g_hMainDlg = NULL;                         // 主对话框句柄
HWND g_hProgressBar = NULL;                     // 进度条句柄
HWND g_hStatusText = NULL;                      // 状态文本句柄
HWND g_hStartButton = NULL;                     // 开始按钮句柄
HWND g_hDetailText = NULL;
bool g_bInstalling = false;                     // 是否正在安装
std::wstring g_installedExePath;                // 已安装程序的路径

// 配置URL
const wchar_t* CONFIG_URL = L"https://zhongliulingshou.oss-cn-hangzhou.aliyuncs.com/app/zmanager/version.json";

// 此代码模块中包含的函数的前向声明:
ATOM                MyRegisterClass(HINSTANCE hInstance);
BOOL                InitInstance(HINSTANCE, int);
LRESULT CALLBACK    WndProc(HWND, UINT, WPARAM, LPARAM);
INT_PTR CALLBACK    About(HWND, UINT, WPARAM, LPARAM);
INT_PTR CALLBACK    InstallerDialogProc(HWND, UINT, WPARAM, LPARAM);

// 新增函数声明
std::wstring DownloadFile(const std::wstring& url, const std::wstring& localPath);
std::wstring DownloadString(const std::wstring& url);
std::wstring GetDocumentsPath();
std::wstring GetDesktopPath();
bool ExtractZip(const std::wstring& zipPath, const std::wstring& extractPath);
bool CreateShortcut(const std::wstring& targetPath, const std::wstring& shortcutPath, const std::wstring& description);
std::wstring WideToUtf8(const std::wstring& wide);
std::wstring Utf8ToWide(const std::string& utf8);
void UpdateStatus(const std::wstring& status);
void UpdateDetail(const std::wstring& detail);
void UpdateProgress(int progress);
void EnableControls(bool enable);
DWORD WINAPI InstallThread(LPVOID lpParam);

// 简单的JSON解析结构
struct UpdateConfig {
    std::wstring package_name;
    std::wstring application_name;
    std::wstring desktop_shortcut;
    std::wstring version;
    std::wstring url;
    std::wstring sign;
};

// 改进的JSON解析函数
UpdateConfig ParseUpdateConfig(const std::wstring& jsonContent) {
    UpdateConfig config;
    
    try {
        // 使用正则表达式进行更可靠的JSON解析
        std::wstring content = jsonContent;
        
        // 解析package_name（根级别）
        std::wregex packageRegex(L"\"package_name\"\\s*:\\s*\"([^\"]+)\"");
        std::wsmatch packageMatch;
        if (std::regex_search(content, packageMatch, packageRegex)) {
            config.package_name = packageMatch[1].str();
        }
        
        // 查找windows节点
        std::wregex windowsStartRegex(L"\"windows\"\\s*:\\s*\\{");
        std::wsmatch windowsStartMatch;
        if (std::regex_search(content, windowsStartMatch, windowsStartRegex)) {
            size_t startPos = windowsStartMatch.position() + windowsStartMatch.length();
            
            // 找到windows节点的结束位置
            int braceCount = 1;
            size_t endPos = startPos;
            for (size_t i = startPos; i < content.length(); i++) {
                if (content[i] == L'{') braceCount++;
                else if (content[i] == L'}') {
                    braceCount--;
                    if (braceCount == 0) {
                        endPos = i;
                        break;
                    }
                }
            }
            
            if (endPos > startPos) {
                std::wstring windowsSection = content.substr(startPos, endPos - startPos);
                
                // 解析windows节点内的各个字段
                auto parseField = [&windowsSection](const std::wstring& fieldName) -> std::wstring {
                    std::wstring pattern = L"\"" + fieldName + L"\"\\s*:\\s*\"([^\"]+)\"";
                    std::wregex fieldRegex(pattern);
                    std::wsmatch fieldMatch;
                    if (std::regex_search(windowsSection, fieldMatch, fieldRegex)) {
                        return fieldMatch[1].str();
                    }
                    return L"";
                };
                
                config.application_name = parseField(L"application_name");
                config.desktop_shortcut = parseField(L"desktop_shortcut");
                config.version = parseField(L"version");
                config.url = parseField(L"url");
                config.sign = parseField(L"sign");
            }
        }
        
    } catch (const std::regex_error& e) {
        UpdateDetail(L"JSON解析失败：正则表达式错误");
        
        // 回退到简单字符串解析
        auto findValue = [&jsonContent](const std::wstring& key) -> std::wstring {
            std::wstring searchKey = L"\"" + key + L"\":\"";
            size_t pos = jsonContent.find(searchKey);
            if (pos != std::wstring::npos) {
                pos += searchKey.length();
                size_t endPos = jsonContent.find(L"\"", pos);
                if (endPos != std::wstring::npos) {
                    return jsonContent.substr(pos, endPos - pos);
                }
            }
            return L"";
        };
        
        // 查找package_name（在根级别）
        config.package_name = findValue(L"package_name");
        
        // 查找windows节点下的值
        size_t windowsStart = jsonContent.find(L"\"windows\":{");
        if (windowsStart != std::wstring::npos) {
            size_t windowsEnd = jsonContent.find(L"}", windowsStart);
            if (windowsEnd != std::wstring::npos) {
                std::wstring windowsSection = jsonContent.substr(windowsStart, windowsEnd - windowsStart);
                
                // 在windows节点内查找各个字段
                auto findValueInSection = [&windowsSection](const std::wstring& key) -> std::wstring {
                    std::wstring searchKey = L"\"" + key + L"\":\"";
                    size_t pos = windowsSection.find(searchKey);
                    if (pos != std::wstring::npos) {
                        pos += searchKey.length();
                        size_t endPos = windowsSection.find(L"\"", pos);
                        if (endPos != std::wstring::npos) {
                            return windowsSection.substr(pos, endPos - pos);
                        }
                    }
                    return L"";
                };
                
                config.application_name = findValueInSection(L"application_name");
                config.desktop_shortcut = findValueInSection(L"desktop_shortcut");
                config.version = findValueInSection(L"version");
                config.url = findValueInSection(L"url");
                config.sign = findValueInSection(L"sign");
            }
        }
    }
    
    return config;
}

// 下载字符串内容
std::wstring DownloadString(const std::wstring& url) {
    HINTERNET hInternet = InternetOpen(L"Installer", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) {
        return L"";
    }
    
    HINTERNET hUrl = InternetOpenUrl(hInternet, url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
    if (!hUrl) {
        InternetCloseHandle(hInternet);
        return L"";
    }
    
    std::string content;
    char buffer[BUFFER_SIZE];
    DWORD bytesRead;
    
    while (InternetReadFile(hUrl, buffer, BUFFER_SIZE - 1, &bytesRead) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        content += buffer;
    }
    
    InternetCloseHandle(hUrl);
    InternetCloseHandle(hInternet);
    
    return Utf8ToWide(content);
}

// 下载文件
std::wstring DownloadFile(const std::wstring& url, const std::wstring& localPath) {
    HINTERNET hInternet = InternetOpen(L"Installer", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) {
         UpdateDetail(L"请检查网络连接");
        return L"";
    }
    
    HINTERNET hUrl = InternetOpenUrl(hInternet, url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
    if (!hUrl) {
        UpdateDetail(L"请检查网络连接和下载地址");
        InternetCloseHandle(hInternet);
        return L"";
    }
    
    // 确保目标目录存在
    std::wstring directory = localPath.substr(0, localPath.find_last_of(L"\\/"));
    if (!directory.empty()) {
        // 创建目录（包括多级目录）
        std::wstring currentDir;
        size_t pos = 0;
        while ((pos = directory.find(L"\\", pos)) != std::wstring::npos) {
            currentDir = directory.substr(0, pos);
            if (!currentDir.empty()) {
                CreateDirectoryW(currentDir.c_str(), NULL);
            }
            pos++;
        }
        // 创建最终目录
        CreateDirectoryW(directory.c_str(), NULL);
    }
    
    std::ofstream file(localPath, std::ios::binary);
    if (!file.is_open()) {
        UpdateDetail(L"文件打开失败，请检查目录权限");
        InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        return L"";
    }
    
    char buffer[BUFFER_SIZE];
    DWORD bytesRead;
    
    while (InternetReadFile(hUrl, buffer, BUFFER_SIZE, &bytesRead) && bytesRead > 0) {
        file.write(buffer, bytesRead);
    }
    
    file.close();
    InternetCloseHandle(hUrl);
    InternetCloseHandle(hInternet);
    
    return localPath;
}

// 获取文档路径
std::wstring GetDocumentsPath() {
    wchar_t path[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_MYDOCUMENTS, NULL, 0, path))) {
        return std::wstring(path);
    }
    return L"";
}

// 获取桌面路径
std::wstring GetDesktopPath() {
    wchar_t path[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathW(NULL, CSIDL_DESKTOP, NULL, 0, path))) {
        return std::wstring(path);
    }
    return L"";
}

// 简单的ZIP解压（使用Windows内置功能）
bool ExtractZip(const std::wstring& zipPath, const std::wstring& extractPath) {
    // 创建解压目录
    CreateDirectoryW(extractPath.c_str(), NULL);
    
    // 使用PowerShell解压ZIP文件
    std::wstring command = L"powershell.exe -command \"Expand-Archive -Path '" + zipPath + L"' -DestinationPath '" + extractPath + L"' -Force\"";
    
    STARTUPINFOW si = {0};
    PROCESS_INFORMATION pi = {0};
    si.cb = sizeof(si);
    
    if (CreateProcessW(NULL, (LPWSTR)command.c_str(), NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        WaitForSingleObject(pi.hProcess, INFINITE);
        DWORD exitCode;
        GetExitCodeProcess(pi.hProcess, &exitCode);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        return exitCode == 0;
    }
    
    return false;
}

// 创建快捷方式
bool CreateShortcut(const std::wstring& targetPath, const std::wstring& shortcutPath, const std::wstring& description) {
    IShellLinkW* pShellLink = NULL;
    IPersistFile* pPersistFile = NULL;
    
    HRESULT hr = CoCreateInstance(CLSID_ShellLink, NULL, CLSCTX_INPROC_SERVER, IID_IShellLinkW, (void**)&pShellLink);
    if (FAILED(hr)) return false;
    
    hr = pShellLink->SetPath(targetPath.c_str());
    if (FAILED(hr)) {
        pShellLink->Release();
        return false;
    }
    
    hr = pShellLink->SetDescription(description.c_str());
    if (FAILED(hr)) {
        pShellLink->Release();
        return false;
    }
    
    hr = pShellLink->QueryInterface(IID_IPersistFile, (void**)&pPersistFile);
    if (FAILED(hr)) {
        pShellLink->Release();
        return false;
    }
    
    hr = pPersistFile->Save(shortcutPath.c_str(), TRUE);
    
    pPersistFile->Release();
    pShellLink->Release();
    
    return SUCCEEDED(hr);
}

// 字符串转换函数
std::wstring WideToUtf8(const std::wstring& wide) {
    if (wide.empty()) return L"";
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wide.data(), (int)wide.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wide.data(), (int)wide.size(), &strTo[0], size_needed, NULL, NULL);
    return std::wstring(strTo.begin(), strTo.end());
}

std::wstring Utf8ToWide(const std::string& utf8) {
    if (utf8.empty()) return L"";
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, utf8.data(), (int)utf8.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, utf8.data(), (int)utf8.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

// 界面更新函数
void UpdateStatus(const std::wstring& status) {
    if (g_hStatusText) {
        SetWindowTextW(g_hStatusText, status.c_str());
    }
}

void UpdateDetail(const std::wstring& detail) {
    if (g_hDetailText) {
        SetWindowTextW(g_hDetailText, detail.c_str());
    }
}

void UpdateProgress(int progress) {
    if (g_hProgressBar) {
        SendMessage(g_hProgressBar, PBM_SETPOS, progress, 0);
    }
}

void EnableControls(bool enable) {
    if (g_hStartButton) {
        EnableWindow(g_hStartButton, enable);
    }
}

// 安装线程函数
DWORD WINAPI InstallThread(LPVOID lpParam) {
    // 初始化COM
    CoInitialize(NULL);
    
    try {
        // 1. 下载配置文件
        UpdateStatus(L"正在下载配置文件...");
        UpdateProgress(10);
        std::wstring jsonContent = DownloadString(CONFIG_URL);
        if (jsonContent.empty()) {
            UpdateStatus(L"错误：无法下载配置文件");
            UpdateDetail(L"请检查网络连接和配置文件URL是否正确");
            goto cleanup;
        }
        
        // 2. 解析配置
        UpdateStatus(L"正在解析配置...");
        UpdateProgress(20);
        UpdateDetail(L"解析JSON配置文件...");
        UpdateConfig config = ParseUpdateConfig(jsonContent);
        if (config.url.empty() || config.package_name.empty()) {
            UpdateStatus(L"错误：配置文件格式错误");
            UpdateDetail(L"配置文件缺少必要的字段");
            goto cleanup;
        }
        
        // 显示解析的配置信息
        std::wstring configInfo = L"解析配置成功:\n";
        configInfo += L"包名: " + config.package_name + L"\n";
        configInfo += L"版本: " + config.version + L"\n";
        configInfo += L"应用名: " + config.application_name + L"\n";
        configInfo += L"快捷方式: " + config.desktop_shortcut + L"\n";
        configInfo += L"下载地址: " + config.url;
        UpdateDetail(configInfo);
        
        // 3. 创建安装目录
        UpdateStatus(L"正在创建安装目录...");
        UpdateProgress(30);
        std::wstring documentsPath = GetDocumentsPath();
        if (documentsPath.empty()) {
            UpdateStatus(L"错误：无法获取文档目录");
            UpdateDetail(L"请检查用户权限");
            goto cleanup;
        }
        
        std::wstring installDir = documentsPath + L"\\" + config.package_name + L"\\" + config.version;
        CreateDirectoryW(installDir.c_str(), NULL);
        
        UpdateDetail(L"安装目录: " + installDir);
        
        // 4. 下载安装包
        UpdateStatus(L"正在下载安装包...");
        UpdateProgress(50);
        std::wstring zipPath = installDir + L"\\package.zip";
        std::wstring downloadedPath = DownloadFile(config.url, zipPath);
        if (downloadedPath.empty()) {
            UpdateStatus(L"错误：无法下载安装包");
            // UpdateDetail(L"请检查网络连接和下载地址");
            goto cleanup;
        }
        
        UpdateDetail(L"下载完成: " + downloadedPath);
        
        // 5. 解压安装包
        UpdateStatus(L"正在解压安装包...");
        UpdateProgress(70);
        if (!ExtractZip(zipPath, installDir)) {
            UpdateStatus(L"错误：无法解压安装包");
            UpdateDetail(L"请检查ZIP文件是否损坏");
            goto cleanup;
        }
        
        UpdateDetail(L"解压完成");
        
        // 6. 创建桌面快捷方式
        UpdateStatus(L"正在创建快捷方式...");
        UpdateProgress(90);
        if (!config.application_name.empty() && !config.desktop_shortcut.empty()) {
            std::wstring exePath = installDir + L"\\" + config.application_name;
            std::wstring desktopPath = GetDesktopPath();
            std::wstring shortcutPath = desktopPath + L"\\" + config.desktop_shortcut + L".lnk";
            
            UpdateDetail(L"正在创建快捷方式...\n目标: " + exePath + L"\n快捷方式: " + shortcutPath);
            
            if (CreateShortcut(exePath, shortcutPath, config.desktop_shortcut)) {
                UpdateStatus(L"安装完成！桌面快捷方式已创建。");
                UpdateDetail(L"安装成功完成！");
                g_installedExePath = exePath;
            } else {
                UpdateStatus(L"安装完成，但创建快捷方式失败。");
                UpdateDetail(L"应用程序已安装，但快捷方式创建失败");
            }
        } else {
            UpdateStatus(L"安装完成！");
            UpdateDetail(L"安装成功完成！");
            g_installedExePath = installDir + L"\\" + config.application_name;
        }
        
        // 7. 清理临时文件
        UpdateProgress(100);
        DeleteFileW(zipPath.c_str());
        
    } catch (...) {
        UpdateStatus(L"错误：安装过程中发生未知错误");
        UpdateDetail(L"请重新运行安装程序");
    }
    
cleanup:
    // 清理COM
    CoUninitialize();
    
    // 恢复界面状态
    g_bInstalling = false;
    EnableControls(true);
    
    // 如果安装成功，按钮变成"打开程序"
    if (!g_installedExePath.empty()) {
        SetWindowTextW(g_hStartButton, L"打开程序");
    } else {
        SetWindowTextW(g_hStartButton, L"重新安装");
    }
    
    return 0;
}

// 安装程序对话框过程
INT_PTR CALLBACK InstallerDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam) {
    switch (message) {
    case WM_INITDIALOG:
        {
            // 保存控件句柄
            g_hMainDlg = hDlg;
            g_hStatusText = GetDlgItem(hDlg, IDC_STATUS_TEXT);
            g_hProgressBar = GetDlgItem(hDlg, IDC_PROGRESS);
            g_hStartButton = GetDlgItem(hDlg, IDC_START_BUTTON);
            g_hDetailText = GetDlgItem(hDlg, IDC_DETAIL_TEXT);
            
            // 设置详细信息文本控件支持自动换行
            if (g_hDetailText) {
                LONG_PTR style = GetWindowLongPtr(g_hDetailText, GWL_STYLE);
                SetWindowLongPtr(g_hDetailText, GWL_STYLE, style);
            }
            
            // 初始化进度条
            if (g_hProgressBar) {
                SendMessage(g_hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, 100));
                SendMessage(g_hProgressBar, PBM_SETSTEP, 1, 0);
            }
            
            // 初始化状态
            UpdateStatus(L"准备就绪，点击开始安装");
            UpdateProgress(0);
            
            return (INT_PTR)TRUE;
        }
        
    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            switch (wmId) {
            case IDC_START_BUTTON:
                if (!g_bInstalling) {
                    if (g_installedExePath.empty()) {
                        // 开始安装
                        g_bInstalling = true;
                        EnableControls(false);
                        SetWindowTextW(g_hStartButton, L"安装中...");
                        
                        // 启动安装线程
                        CreateThread(NULL, 0, InstallThread, NULL, 0, NULL);
                    } else {
                        // 打开程序并退出安装程序
                        ShellExecute(NULL, L"open", g_installedExePath.c_str(), NULL, NULL, SW_SHOWNORMAL);
                        EndDialog(hDlg, IDOK);
                    }
                }
                break;
                
            case IDM_EXIT:
                EndDialog(hDlg, LOWORD(wParam));
                break;
            }
        }
        break;
        
    case WM_CLOSE:
        EndDialog(hDlg, LOWORD(wParam));
        break;
    }
    return (INT_PTR)FALSE;
}

int APIENTRY wWinMain(_In_ HINSTANCE hInstance,
                     _In_opt_ HINSTANCE hPrevInstance,
                     _In_ LPWSTR    lpCmdLine,
                     _In_ int       nCmdShow)
{
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);

    // 初始化通用控件
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_PROGRESS_CLASS | ICC_STANDARD_CLASSES | ICC_WIN95_CLASSES;
    InitCommonControlsEx(&icex);

    // 显示安装程序对话框
    DialogBox(hInstance, MAKEINTRESOURCE(IDD_INSTALLER_DIALOG), NULL, InstallerDialogProc);
    
    return 0;
}

//
//  函数: MyRegisterClass()
//
//  目标: 注册窗口类。
//
ATOM MyRegisterClass(HINSTANCE hInstance)
{
    WNDCLASSEXW wcex;

    wcex.cbSize = sizeof(WNDCLASSEX);

    wcex.style          = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc    = WndProc;
    wcex.cbClsExtra     = 0;
    wcex.cbWndExtra     = 0;
    wcex.hInstance      = hInstance;
    wcex.hIcon          = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_INSTALLER));
    wcex.hCursor        = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground  = (HBRUSH)(COLOR_WINDOW+1);
    wcex.lpszMenuName   = MAKEINTRESOURCEW(IDC_INSTALLER);
    wcex.lpszClassName  = szWindowClass;
    wcex.hIconSm        = LoadIcon(wcex.hInstance, MAKEINTRESOURCE(IDI_SMALL));

    return RegisterClassExW(&wcex);
}

//
//   函数: InitInstance(HINSTANCE, int)
//
//   目标: 保存实例句柄并创建主窗口
//
//   注释:
//
//        在此函数中，我们在全局变量中保存实例句柄并
//        创建和显示主程序窗口。
//
BOOL InitInstance(HINSTANCE hInstance, int nCmdShow)
{
   hInst = hInstance; // 将实例句柄存储在全局变量中

   HWND hWnd = CreateWindowW(szWindowClass, szTitle, WS_OVERLAPPEDWINDOW,
      CW_USEDEFAULT, 0, CW_USEDEFAULT, 0, nullptr, nullptr, hInstance, nullptr);

   if (!hWnd)
   {
      return FALSE;
   }

   ShowWindow(hWnd, nCmdShow);
   UpdateWindow(hWnd);

   return TRUE;
}

//
//  函数: WndProc(HWND, UINT, WPARAM, LPARAM)
//
//  目标: 处理主窗口的消息。
//
//  WM_COMMAND  - 处理应用程序菜单
//  WM_PAINT    - 绘制主窗口
//  WM_DESTROY  - 发送退出消息并返回
//
//
LRESULT CALLBACK WndProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case WM_COMMAND:
        {
            int wmId = LOWORD(wParam);
            // 分析菜单选择:
            switch (wmId)
            {
            case IDM_ABOUT:
                DialogBox(hInst, MAKEINTRESOURCE(IDD_ABOUTBOX), hWnd, About);
                break;
            case IDM_EXIT:
                DestroyWindow(hWnd);
                break;
            default:
                return DefWindowProc(hWnd, message, wParam, lParam);
            }
        }
        break;
    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);
            // TODO: 在此处添加使用 hdc 的任何绘图代码...
            EndPaint(hWnd, &ps);
        }
        break;
    case WM_DESTROY:
        PostQuitMessage(0);
        break;
    default:
        return DefWindowProc(hWnd, message, wParam, lParam);
    }
    return 0;
}

// "关于"框的消息处理程序。
INT_PTR CALLBACK About(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    UNREFERENCED_PARAMETER(lParam);
    switch (message)
    {
    case WM_INITDIALOG:
        return (INT_PTR)TRUE;

    case WM_COMMAND:
        if (LOWORD(wParam) == IDOK || LOWORD(wParam) == IDCANCEL)
        {
            EndDialog(hDlg, LOWORD(wParam));
            return (INT_PTR)TRUE;
        }
        break;
    }
    return (INT_PTR)FALSE;
}
