@echo off
echo 极简安装程序编译脚本
echo.

REM 检查是否存在Visual Studio
set "VS_PATH="
for /f "tokens=*" %%i in ('dir /b /s "C:\Program Files\Microsoft Visual Studio\*\*\MSBuild\Current\Bin\MSBuild.exe" 2^>nul') do (
    set "VS_PATH=%%i"
    goto :found
)

for /f "tokens=*" %%i in ('dir /b /s "C:\Program Files (x86)\Microsoft Visual Studio\*\*\MSBuild\Current\Bin\MSBuild.exe" 2^>nul') do (
    set "VS_PATH=%%i"
    goto :found
)

echo 错误：未找到Visual Studio或MSBuild
echo 请确保已安装Visual Studio 2019或更高版本
echo.
echo 或者您可以：
echo 1. 使用Visual Studio IDE打开installer.sln
echo 2. 选择Release x64配置
echo 3. 点击"生成解决方案"
pause
exit /b 1

:found
echo 找到MSBuild: %VS_PATH%
echo.

REM 编译项目
echo 正在编译项目...
"%VS_PATH%" installer\installer.vcxproj /p:Configuration=Release /p:Platform=x64 /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo 可执行文件位置: x64\Release\installer.exe
    echo.
    echo 重要说明：
    echo 此版本已配置为静态链接运行时库，无需额外的DLL文件
    echo 可以在没有安装Visual C++ Redistributable的机器上运行
    echo.
    echo 使用方法：
    echo 1. 修改 installer\installer.cpp 中的 CONFIG_URL
    echo 2. 直接运行 x64\Release\installer.exe
    echo 3. 程序可以独立运行，无需依赖MSVCP140.dll等文件
    echo.
    echo 测试JSON解析：
    echo 1. 编译 test_json.cpp 来测试JSON解析功能
    echo 2. 运行测试程序验证解析结果
    echo.
    echo 如果仍然遇到DLL问题，请尝试：
    echo 1. 重新编译项目
    echo 2. 检查目标机器是否缺少其他系统DLL
    echo 3. 使用Dependency Walker工具分析依赖关系
) else (
    echo.
    echo 编译失败！
    echo 请检查错误信息并修复问题
)

pause 