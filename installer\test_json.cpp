#include <iostream>
#include <string>
#include <regex>

struct UpdateConfig {
    std::wstring package_name;
    std::wstring application_name;
    std::wstring desktop_shortcut;
    std::wstring version;
    std::wstring url;
    std::wstring sign;
};

// 改进的JSON解析函数
UpdateConfig ParseUpdateConfig(const std::wstring& jsonContent) {
    UpdateConfig config;
    
    try {
        // 使用正则表达式进行更可靠的JSON解析
        std::wstring content = jsonContent;
        
        // 解析package_name（根级别）
        std::wregex packageRegex(L"\"package_name\"\\s*:\\s*\"([^\"]+)\"");
        std::wsmatch packageMatch;
        if (std::regex_search(content, packageMatch, packageRegex)) {
            config.package_name = packageMatch[1].str();
        }
        
        // 查找windows节点
        std::wregex windowsStartRegex(L"\"windows\"\\s*:\\s*\\{");
        std::wsmatch windowsStartMatch;
        if (std::regex_search(content, windowsStartMatch, windowsStartRegex)) {
            size_t startPos = windowsStartMatch.position() + windowsStartMatch.length();
            
            // 找到windows节点的结束位置
            int braceCount = 1;
            size_t endPos = startPos;
            for (size_t i = startPos; i < content.length(); i++) {
                if (content[i] == L'{') braceCount++;
                else if (content[i] == L'}') {
                    braceCount--;
                    if (braceCount == 0) {
                        endPos = i;
                        break;
                    }
                }
            }
            
            if (endPos > startPos) {
                std::wstring windowsSection = content.substr(startPos, endPos - startPos);
                
                // 解析windows节点内的各个字段
                auto parseField = [&windowsSection](const std::wstring& fieldName) -> std::wstring {
                    std::wstring pattern = L"\"" + fieldName + L"\"\\s*:\\s*\"([^\"]+)\"";
                    std::wregex fieldRegex(pattern);
                    std::wsmatch fieldMatch;
                    if (std::regex_search(windowsSection, fieldMatch, fieldRegex)) {
                        return fieldMatch[1].str();
                    }
                    return L"";
                };
                
                config.application_name = parseField(L"application_name");
                config.desktop_shortcut = parseField(L"desktop_shortcut");
                config.version = parseField(L"version");
                config.url = parseField(L"url");
                config.sign = parseField(L"sign");
            }
        }
        
    } catch (const std::regex_error& e) {
        std::wcout << L"JSON解析失败：正则表达式错误" << std::endl;
    }
    
    return config;
}

// 字符串转换函数
std::wstring Utf8ToWide(const std::string& utf8) {
    if (utf8.empty()) return L"";
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, utf8.data(), (int)utf8.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, utf8.data(), (int)utf8.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

int main() {
    // 测试JSON字符串
    std::string testJson = R"({
        "package_name": "pro.shengyi.knife",
        "windows": {
            "application_name": "knife_design.exe",
            "desktop_shortcut": "众流BI编辑器",
            "version": "1.0.8",
            "url": "https://zhongliulingshou.oss-cn-hangzhou.aliyuncs.com/app/knife/Release.zip",
            "sign": "fd09b58e8e30c651c084cf0a85d9001e"
        }
    })";
    
    std::wstring wideJson = Utf8ToWide(testJson);
    
    std::wcout << L"测试JSON解析..." << std::endl;
    std::wcout << L"输入JSON: " << wideJson << std::endl;
    
    UpdateConfig config = ParseUpdateConfig(wideJson);
    
    std::wcout << L"\n解析结果:" << std::endl;
    std::wcout << L"package_name: " << config.package_name << std::endl;
    std::wcout << L"application_name: " << config.application_name << std::endl;
    std::wcout << L"desktop_shortcut: " << config.desktop_shortcut << std::endl;
    std::wcout << L"version: " << config.version << std::endl;
    std::wcout << L"url: " << config.url << std::endl;
    std::wcout << L"sign: " << config.sign << std::endl;
    
    return 0;
} 