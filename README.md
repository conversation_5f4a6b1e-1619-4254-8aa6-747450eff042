# 极简安装程序

这是一个极简的Windows安装程序，具有简洁的图形界面和完整的安装流程。

## 安装目录结构

安装程序会将应用程序安装到以下目录：
```
我的文档/package_name/version/
```

例如：
```
C:\Users\<USER>\Documents\pro.shengyi.knife\1.0.8\
```

## JSON配置文件格式

配置文件应包含以下结构：

```json
{
    "package_name": "应用包名",
    "windows": {
        "application_name": "主程序文件名.exe",
        "desktop_shortcut": "桌面快捷方式名称",
        "version": "版本号",
        "url": "下载地址",
        "sign": "签名验证码"
    }
}
```

## JSON解析特性

- **正则表达式解析**：使用C++标准库的正则表达式进行可靠的JSON解析
- **嵌套对象支持**：正确解析嵌套的JSON对象结构
- **错误处理**：包含异常处理和回退机制
- **UTF-8支持**：完全支持中文等Unicode字符

## 安装流程

1. **启动程序**：运行installer.exe
2. **点击开始**：点击"开始安装"按钮
3. **自动下载**：程序自动下载配置文件
4. **解析配置**：解析JSON配置信息
5. **创建目录**：在文档目录创建安装目录
6. **下载安装包**：下载ZIP格式的安装包
7. **解压文件**：解压安装包到目标目录
8. **创建快捷方式**：在桌面创建应用程序快捷方式
9. **完成安装**：显示安装完成信息
10. **打开程序**：点击"打开程序"按钮启动应用程序

## 编译说明

1. 使用Visual Studio 2022打开`installer.sln`
2. 选择Release x64配置
3. 编译生成`installer.exe`

## 技术实现

- 使用WinINet API进行网络下载
- 使用正则表达式进行JSON解析
- 使用PowerShell命令解压ZIP文件
- 使用Windows Shell API创建快捷方式
- 使用Windows通用控件（进度条等）
- 使用ShellExecute启动已安装的程序
- 支持UTF-8编码的JSON配置
- 多线程处理，界面响应流畅
- 完整的Windows主题支持（通过应用程序清单）
- DPI感知支持，适配高分辨率显示器

## 注意事项

1. 确保网络连接正常
2. 确保有足够的磁盘空间
3. 确保对目标目录有写入权限
4. 配置文件URL需要在代码中设置
5. 需要Windows 10或更高版本

## 自定义配置

在`installer.cpp`中修改`CONFIG_URL`常量来设置配置文件地址：

```cpp
const wchar_t* CONFIG_URL = L"https://your-server.com/update.json";
```

## 界面截图

安装程序界面包含：
- 状态显示区域
- 进度条
- 开始安装按钮
- 打开程序按钮

## 测试

项目包含一个测试文件`test_json.cpp`，可以用来验证JSON解析功能：

```bash
# 编译测试程序
g++ -o test_json test_json.cpp

# 运行测试
./test_json
```

std::wstring g_installedExePath;  // 保存已安装程序的路径 

case IDC_OPEN_BUTTON:
    if (!g_installedExePath.empty()) {
        ShellExecute(NULL, L"open", g_installedExePath.c_str(), NULL, NULL, SW_SHOWNORMAL);
    }
    break; 