# MSVCP140.dll 缺失问题解决方案

## 问题描述
编译后的EXE程序在运行时提示找不到 `MSVCP140.dll` 文件。这是因为程序使用了Visual C++运行时库，而目标机器上没有安装相应的Visual C++ Redistributable。

## 解决方案

### 方案1：静态链接运行时库（推荐）✅

**优点：**
- 生成的EXE文件完全独立，无需额外DLL
- 可以在任何Windows机器上运行
- 部署简单，只需要一个EXE文件

**缺点：**
- EXE文件稍大一些
- 每个程序都包含运行时库的副本

**实施步骤：**
1. 项目配置已经修改完成
2. 重新编译项目：运行 `build.bat` 或在Visual Studio中重新生成
3. 生成的EXE文件将不再依赖MSVCP140.dll

**配置详情：**
- Release配置：`RuntimeLibrary = MultiThreaded`
- Debug配置：`RuntimeLibrary = MultiThreadedDebug`

### 方案2：分发Visual C++ Redistributable

**如果您仍然希望使用动态链接：**

1. **下载并安装Visual C++ Redistributable：**
   - 访问Microsoft官网下载页面
   - 下载对应版本的vc_redist.x64.exe（64位）或vc_redist.x86.exe（32位）
   - 在目标机器上安装

2. **与程序一起分发：**
   - 将vc_redist.x64.exe包含在安装包中
   - 在安装程序中自动安装Redistributable

### 方案3：手动复制DLL文件（不推荐）

将以下DLL文件复制到EXE同目录：
- MSVCP140.dll
- VCRUNTIME140.dll
- 可能还需要其他相关DLL

**注意：** 此方案可能违反Microsoft的许可协议，不推荐使用。

## 验证解决方案

### 测试步骤：
1. 重新编译项目
2. 将生成的EXE复制到一台没有安装Visual Studio的机器上
3. 直接运行EXE，确认不再出现DLL缺失错误

### 检查工具：
- **Dependency Walker：** 分析EXE的DLL依赖关系
- **Process Monitor：** 监控程序运行时的文件访问

## 编译说明

### 使用批处理脚本：
```batch
build.bat
```

### 使用Visual Studio：
1. 打开 `installer.sln`
2. 选择 `Release` 配置和 `x64` 平台
3. 点击"生成解决方案"

### 验证静态链接：
编译完成后，生成的EXE文件应该：
- 文件大小相对较大（包含了运行时库）
- 可以在没有Visual C++ Redistributable的机器上运行

## 常见问题

### Q: 编译后文件变大了？
A: 这是正常的，因为运行时库被静态链接到了EXE中。

### Q: 仍然提示缺少其他DLL？
A: 可能是系统DLL（如kernel32.dll），这些是Windows系统自带的，检查目标系统版本兼容性。

### Q: 想要恢复动态链接？
A: 将项目配置中的RuntimeLibrary改回：
- Release: `MultiThreadedDLL`
- Debug: `MultiThreadedDebugDLL`

## 项目配置变更记录

已修改的配置项：
- `Debug|Win32`: RuntimeLibrary = MultiThreadedDebug
- `Release|Win32`: RuntimeLibrary = MultiThreaded  
- `Debug|x64`: RuntimeLibrary = MultiThreadedDebug
- `Release|x64`: RuntimeLibrary = MultiThreaded

这些配置确保了程序使用静态链接的运行时库，从而避免了MSVCP140.dll依赖问题。
